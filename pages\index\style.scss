$primary-color: #e94e3c;
$secondary-color: #ffc200;
$text-primary: #20232a;
$text-secondary: #666666;
$text-light: #999999;
$background-light: #f8f9fa;
$border-light: #ebebeb;
$success-color: #1dc779;
$shadow-light: rgba(0, 0, 0, 0.1);

.page-container {
  background-color: $background-light;
  min-height: 100vh;
}

.home_content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 160rpx;
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: $background-light;
  /* #ifdef H5 */
  // padding-top: 100rpx;
  /* #endif */

  // .navBar {
  // 	position: fixed;
  // 	z-index: 99;
  // 	top: 0;
  // 	height: 160rpx;
  // 	width: 100%;
  // 	padding-top: var(--status-bar-height);
  // 	padding-left: 20rpx;
  // 	box-sizing: border-box;
  // 	background: linear-gradient(90deg, #E94E3C, #E9793C);
  // 	/* #ifdef H5 */
  // 	padding-top: 20rpx;
  // 	/* #endif */
  // 	.leftNav{
  // 		background: linear-gradient(90deg, #E94E3C, #E9793C);
  // 	}
  // 	.logo {
  // 		height: 66rpx;
  // 		width: 184rpx;
  // 	}
  // }
  // .restaurant_info_box{
  // 	position: relative;
  // 	color: $min-font-color;
  // 	width: 100%;
  // 	height: 160rpx;
  // 	background: linear-gradient(90deg, #E94E3C, #E9793C);
  // 	.restaurant_info{
  // 		position: absolute;
  // 		z-index: 9;
  // 		left: 30rpx;
  // 		// transform: translateX(-50%);
  // 		display: flex;
  // 		width: calc(100% - 60rpx);
  // 		// margin:0 auto;
  // 		background: rgba(255,255,255,0.97);
  // 		box-shadow: 0px 2px 5px 0px rgba(69,69,69,0.10);
  // 		border-radius: 8px;
  // 		padding: 40rpx;
  // 		box-sizing: border-box;
  // 		.left_info{
  // 			flex: 1;
  // 			.title{
  // 				color: $min-font-color;
  // 				font-size: 36rpx;
  // 			}
  // 			.position{
  // 				color: $desc-font-color;
  // 				font-size: 36rpx;
  // 			}
  // 		}
  // 		.restaurant_logo{
  // 			.restaurant_logo_img{
  // 				display: block;
  // 				width: 320rpx;
  // 				height: 120rpx;
  // 				border-radius: 16rpx;
  // 			}
  // 		}
  // 	}
  // }
  // 店铺信息卡片
  .restaurant_info_box {
    position: fixed;
    top: 160rpx;
    left: 0;
    right: 0;
    z-index: 10;
    width: 100%;
    background: linear-gradient(184deg, rgba(0, 0, 0, 0.35) 25%, rgba(51, 51, 51, 0) 96%);

    .restaurant_info {
      margin: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 16rpx;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
      overflow: hidden;

      // 店铺头部信息
      .info_header {
        display: flex;
        align-items: center;
        padding: 24rpx;
        border-bottom: 1px solid $border-light;

        .shop_logo {
          margin-right: 24rpx;

          .logo_image {
            width: 96rpx;
            height: 96rpx;
            border-radius: 12rpx;
            background: #f5f5f5;
          }
        }

        .shop_details {
          flex: 1;

          .shop_title {
            display: flex;
            align-items: center;
            margin-bottom: 12rpx;

            .shop_name {
              font-size: 36rpx;
              font-weight: 600;
              color: $text-primary;
              margin-right: 16rpx;
            }

            .business_status {
              padding: 6rpx 16rpx;
              border-radius: 20rpx;
              font-size: 22rpx;
              font-weight: 500;

              &.open {
                background: rgba($success-color, 0.1);
                color: $success-color;
              }

              &.closed {
                background: rgba($text-light, 0.1);
                color: $text-light;
              }
            }
          }

          .shop_info_row {
            display: flex;
            align-items: center;

            .info_item {
              display: flex;
              align-items: center;
              margin-right: 32rpx;

              .info_icon {
                width: 28rpx;
                height: 28rpx;
                margin-right: 8rpx;
              }

              .info_text {
                font-size: 26rpx;
                color: $text-secondary;
              }
            }
          }
        }
      }

      // 店铺详细信息
      .info_footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 24rpx;

        .address_section {
          flex: 1;

          .shop_description {
            font-size: 28rpx;
            color: $text-primary;
            margin-bottom: 8rpx;
            font-weight: 500;
          }

          .shop_address {
            display: flex;
            align-items: center;

            .address_icon {
              width: 24rpx;
              height: 24rpx;
              margin-right: 8rpx;
            }

            .address_text {
              font-size: 24rpx;
              color: $text-light;
              line-height: 1.4;
            }
          }
        }

        .contact_section {
          .phone_button {
            width: 72rpx;
            height: 72rpx;
            background: rgba($primary-color, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .phone_icon {
              width: 32rpx;
              height: 32rpx;
            }
          }
        }
      }
    }
  }

  // 菜单列表容器
  .restaurant_menu_list {
    display: flex;
    width: 100%;
    height: calc(100vh - 400rpx);
    margin-top: 260rpx;
    flex: 1;
    background: #ffffff;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);

    // 左侧分类列表
    .category_sidebar {
      width: 180rpx;
      background: $background-light;
      border-right: 1px solid $border-light;

      .category_scroll {
        height: 100%;
        padding: 16rpx 0;
      }

      .category_item {
        padding: 24rpx 16rpx;
        margin: 8rpx 12rpx;
        border-radius: 12rpx;
        transition: all 0.3s ease;

        .category_name {
          font-size: 28rpx;
          color: $text-secondary;
          text-align: center;
          line-height: 1.3;
          font-weight: 400;

          &.multi_line {
            line-height: 1.2;
          }
        }

        &.active {
          background: #ffffff;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);

          .category_name {
            color: $primary-color;
            font-weight: 600;
          }
        }
      }

      .bottom_spacer {
        height: 160rpx;
      }
    }

    // 右侧菜品容器
    .dishes_container {
      flex: 1;
      background: #ffffff;

      .dishes_list {
        height: 100%;
        padding: 16rpx 0;
      }

      .dish_card {
        display: flex;
        padding: 24rpx;
        border-bottom: 1px solid rgba($border-light, 0.5);
        transition: background-color 0.2s ease;

        &:hover {
          background: rgba($background-light, 0.3);
        }

        // 菜品图片
        .dish_image_wrapper {
          margin-right: 24rpx;

          .dish_image {
            width: 180rpx;
            height: 180rpx;
            border-radius: 12rpx;
            background: $background-light;
          }
        }

        // 菜品内容
        .dish_content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .dish_header {
            .dish_title {
              font-size: 32rpx;
              font-weight: 600;
              color: $text-primary;
              line-height: 1.4;
              margin-bottom: 8rpx;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }

          .dish_description {
            font-size: 24rpx;
            color: $text-secondary;
            line-height: 1.4;
            margin-bottom: 6rpx;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .dish_sales {
            font-size: 22rpx;
            color: $text-light;
            margin-bottom: 16rpx;
          }

          // 底部价格和操作区域
          .dish_footer {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .price_section {
              display: flex;
              align-items: baseline;

              .currency_symbol {
                font-size: 24rpx;
                color: $primary-color;
                margin-right: 4rpx;
              }

              .price_value {
                font-size: 36rpx;
                font-weight: 600;
                color: $primary-color;
                font-family: DIN, DIN-Medium;
              }
            }

            // 数量控制按钮
            .quantity_controls {
              display: flex;
              align-items: center;

              .control_btn {
                width: 60rpx;
                height: 60rpx;

                &.minus_btn {
                  margin-right: 16rpx;
                }

                &.plus_btn {
                  margin-left: 16rpx;
                }
              }

              .quantity_text {
                min-width: 40rpx;
                text-align: center;
                font-size: 28rpx;
                font-weight: 600;
                color: $text-primary;
              }
            }

            // 规格选择按钮
            .spec_button_wrapper {
              .spec_button {
                padding: 12rpx 24rpx;
                background: $secondary-color;
                border-radius: 24rpx;
                font-size: 24rpx;
                font-weight: 500;
                color: $text-primary;
                text-align: center;
              }
            }
          }
        }
      }

      .bottom_spacer {
        height: 160rpx;
      }
    }

    // 空菜品状态
    .empty_dishes {
      flex: 1;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;

      .empty_text {
        font-size: 28rpx;
        color: $text-light;
        text-align: center;
      }
    }
  }

  // 店铺关闭状态
  .shop_closed {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 260rpx;
    padding: 80rpx 0;

    .closed_text {
      font-size: 48rpx;
      color: $text-secondary;
      font-weight: 500;
    }
  }

  // 底部购物车
  .shopping_cart_bar {
    position: fixed;
    bottom: 32rpx;
    left: 30rpx;
    right: 30rpx;
    height: 96rpx;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 48rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
    z-index: 99;
    backdrop-filter: blur(10rpx);

    // 空购物车状态
    .cart_empty {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 24rpx;

      .cart_icon_wrapper {
        position: relative;
        margin-right: 24rpx;

        .cart_icon {
          width: 96rpx;
          height: 96rpx;
        }
      }

      .cart_info {
        flex: 1;

        .total_price {
          display: flex;
          align-items: baseline;

          .currency {
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.7);
            margin-right: 4rpx;
          }

          .amount {
            font-size: 36rpx;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.7);
            font-family: DIN, DIN-Medium;
          }
        }
      }

      .checkout_btn {
        padding: 0 32rpx;
        height: 72rpx;
        line-height: 72rpx;
        border-radius: 36rpx;
        font-size: 28rpx;
        font-weight: 500;
        text-align: center;

        &.disabled {
          background: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.5);
        }
      }
    }

    // 有商品的购物车状态
    .cart_active {
      display: flex;
      align-items: center;
      height: 100%;

      .cart_content {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0 24rpx;

        .cart_icon_wrapper {
          position: relative;
          margin-right: 24rpx;

          .cart_icon {
            width: 96rpx;
            height: 96rpx;
          }

          .item_count {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            min-width: 32rpx;
            height: 32rpx;
            line-height: 32rpx;
            padding: 0 8rpx;
            background: $primary-color;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: 600;
            color: #ffffff;
            text-align: center;
          }
        }

        .cart_info {
          flex: 1;

          .total_price {
            display: flex;
            align-items: baseline;

            .currency {
              font-size: 24rpx;
              color: #ffffff;
              margin-right: 4rpx;
            }

            .amount {
              font-size: 36rpx;
              font-weight: 600;
              color: #ffffff;
              font-family: DIN, DIN-Medium;
            }
          }
        }
      }

      .checkout_btn {
        margin-right: 12rpx;
        padding: 0 32rpx;
        height: 72rpx;
        line-height: 72rpx;
        border-radius: 36rpx;
        font-size: 28rpx;
        font-weight: 500;
        text-align: center;

        &.active {
          background: $secondary-color;
          color: $text-primary;
        }
      }
    }
  }

  .pop_mask {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 9;
    background-color: rgba($color: #000000, $alpha: 0.4);

    .pop {
      width: 60%;
      position: relative;
      top: 40%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);
      background: #fff;
      border-radius: 20rpx;

      .open_table_cont {
        padding-top: 60rpx;

        .cont_tit {
          font-size: 36rpx;
          color: #20232a;
          text-align: center;
        }

        .people_num_act {
          display: flex;
          width: 60%;
          margin: 0 auto;

          .red,
          .add {
            width: 112rpx;
            height: 112rpx;
          }

          .people_num {
            line-height: 112rpx;
            flex: 1;
            text-align: center;
            font-size: 30rpx;
            color: #20232a;
          }
        }
      }

      .butList {
        background: #f7f7f7;
        display: flex;
        text-align: center;
        border-radius: 20rpx;

        .define {
          flex: 1;
          font-size: 36rpx;
          line-height: 100rpx;
        }

        .cancel {
          flex: 1;
          font-size: 36rpx;
          line-height: 100rpx;
        }
      }
    }

    .more_norm_pop {
      width: calc(100vw - 160rpx);
      box-sizing: border-box;
      position: relative;
      top: 50%;
      left: 50%;
      padding: 40rpx;
      transform: translateX(-50%) translateY(-50%);
      background: #fff;
      border-radius: 20rpx;

      .div_big_image {
        width: 100%;
        border-radius: 10rpx;
      }

      .title {
        font-size: 40rpx;
        line-height: 80rpx;
        text-align: center;
        font-weight: bold;
      }

      .items_cont {
        display: flex;
        flex-wrap: wrap;
        margin-left: -14rpx;
        max-height: 50vh;

        .item_row {
          .flavor_name {
            height: 40rpx;
            opacity: 1;
            font-size: 28rpx;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #666666;
            line-height: 40rpx;
            padding-left: 10rpx;
            padding-top: 20rpx;
          }

          .flavor_item {
            display: flex;
            flex-wrap: wrap;

            .item {
              border: 1px solid #ffb302;
              border-radius: 12rpx;
              margin: 20rpx 10rpx;
              padding: 0 26rpx;
              height: 60rpx;
              line-height: 60rpx;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 400;
              color: #333333;
            }

            .act {
              // background: linear-gradient(144deg, #ffda05 18%, #ffb302 80%);
              background: #ffc200;
              border: 1px solid #ffc200;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
            }
          }
        }
      }

      .but_item {
        display: flex;
        position: relative;
        flex: 1;
        padding-left: 10rpx;
        margin: 34rpx 0 -20rpx 0;

        .price {
          text-align: left;
          color: #e94e3c;
          line-height: 88rpx;
          box-sizing: border-box;
          font-size: 48rpx;
          font-family: DIN, DIN-Medium;
          font-weight: 500;

          .ico {
            font-size: 28rpx;
          }
        }

        .active {
          position: absolute;
          right: 0rpx;
          bottom: 20rpx;
          display: flex;

          .dish_add,
          .dish_red {
            display: block;
            width: 72rpx;
            height: 72rpx;
          }

          .dish_number {
            padding: 0 10rpx;
            line-height: 72rpx;
            font-size: 30rpx;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
          }

          .dish_card_add {
            width: 200rpx;
            height: 60rpx;
            line-height: 60rpx;
            text-align: center;
            font-weight: 500;
            font-size: 28rpx;
            opacity: 1;
            // background: linear-gradient(144deg, #ffda05 18%, #ffb302 80%);
            background: #ffc200;
            border-radius: 30rpx;
          }
        }
      }
    }

    .lodding {
      position: relative;
      top: 40%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .lodding_ico {
        width: 160rpx;
        height: 160rpx;
        border-radius: 100%;
      }

      .lodding_text {
        margin-top: 20rpx;
        color: #fff;
        font-size: 28rpx;
        text-align: center;
      }
    }

    .close {
      position: absolute;
      bottom: -180rpx;
      left: 50%;
      transform: translateX(-50%);

      .close_img {
        width: 88rpx;
        height: 88rpx;
      }
    }
  }

  // 底部遮罩
  .bottom_mask {
    position: absolute;
    height: 160rpx;
    width: 100%;
    bottom: 0;
    background: linear-gradient(to top, rgba($background-light, 0.8), transparent);
    pointer-events: none;
  }
}

// 通用样式优化
.class-item {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    transform: translateY(-2rpx);
  }
}

.class-item:last-child {
  min-height: 100vh;
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .home_content {
    .restaurant_info_box {
      .restaurant_info {
        margin: 16rpx 20rpx;

        .info_header {
          padding: 20rpx;

          .shop_logo .logo_image {
            width: 80rpx;
            height: 80rpx;
          }

          .shop_details .shop_title .shop_name {
            font-size: 32rpx;
          }
        }
      }
    }

    .restaurant_menu_list {
      .category_sidebar {
        width: 160rpx;

        .category_item {
          padding: 20rpx 12rpx;

          .category_name {
            font-size: 26rpx;
          }
        }
      }

      .dishes_container .dish_card {
        padding: 20rpx;

        .dish_image_wrapper .dish_image {
          width: 160rpx;
          height: 160rpx;
        }

        .dish_content {
          .dish_header .dish_title {
            font-size: 30rpx;
          }

          .dish_footer .price_section .price_value {
            font-size: 32rpx;
          }
        }
      }
    }
  }
}