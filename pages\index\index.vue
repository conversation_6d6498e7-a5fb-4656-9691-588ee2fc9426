<template>
  <view class="page-container">
    <!-- 导航 -->
    <navBar></navBar>

    <view class="home_content" :style="{ paddingTop: ht + 'px' }">
      <!-- 店铺基本信息 -->
      <view class="restaurant_info_box">
        <view class="restaurant_info">
          <!-- 店铺头部信息 -->
          <view class="info_header">
            <view class="shop_logo">
              <image class="logo_image" src="../../static/logo_ruiji.png"></image>
            </view>
            <view class="shop_details">
              <view class="shop_title">
                <text class="shop_name">{{ currentShopInfo.shopName }}</text>
                <view class="business_status" :class="{ 'open': shopStatus === 1, 'closed': shopStatus !== 1 }">
                  {{ shopStatus === 1 ? '营业中' : '休息中' }}
                </view>
              </view>
              <view class="shop_info_row">
                <view class="info_item">
                  <image class="info_icon" src="../../static/money.png"></image>
                  <text class="info_text">配送费{{ deliveryFee() }}元</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 店铺详细信息 -->
          <view class="info_footer">
            <view class="address_section">
              <view class="shop_description">{{ currentShopInfo.shopName }}</view>
              <view class="shop_address">
                <image class="address_icon" src="../../image/address.png"></image>
                <text class="address_text">{{ currentShopInfo.shopAddress }}</text>
              </view>
            </view>
            <view class="contact_section">
              <view class="phone_button" @click="handlePhone('bottom')">
                <image class="phone_icon" src="../../image/phone.png"></image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 菜单列表 -->
      <view class="restaurant_menu_list" v-if="shopStatus === 1">
        <!-- 左侧分类列表 -->
        <view class="category_sidebar">
          <scroll-view scroll-y scroll-with-animation class="category_scroll" :scroll-top="scrollTop + 100"
            :scroll-into-view="itemId">
            <view class="category_item" :class="{ 'active': typeIndex == index }"
              v-for="(item, index) in typeListData" :key="index" @tap.stop="swichMenu(item, index)">
              <view class="category_name" :class="{ 'multi_line': item.name.length > 5 }">{{ item.name }}</view>
            </view>
            <view class="bottom_spacer"></view>
          </scroll-view>
        </view>

        <!-- 右侧菜品列表 -->
        <view class="dishes_container">
          <scroll-view class="dishes_list" scroll-y="true" scroll-top="0rpx"
            v-if="dishListItems && dishListItems.length > 0">
            <view class="dish_card" v-for="(item, index) in dishListItems" :key="index">
              <!-- 菜品图片 -->
              <view class="dish_image_wrapper" @click="openDetailHandle(item)">
                <image mode="aspectFill" :src="item.image" class="dish_image"></image>
              </view>

              <!-- 菜品信息 -->
              <view class="dish_content">
                <view class="dish_header" @click="openDetailHandle(item)">
                  <text class="dish_title">{{ item.name }}</text>
                </view>
                <view class="dish_description" @click="openDetailHandle(item)">
                  {{ item.description || item.name }}
                </view>
                <view class="dish_sales" @click="openDetailHandle(item)">月销量0</view>

                <view class="dish_footer">
                  <view class="price_section">
                    <text class="currency_symbol">￥</text>
                    <text class="price_value">{{ item.price.toFixed(2) }}</text>
                  </view>

                  <!-- 普通菜品的加减按钮 -->
                  <view class="quantity_controls" v-if="!item.flavors || item.flavors.length === 0">
                    <image v-if="item.dishNumber >= 1" src="../../static/btn_red.png"
                      @click="redDishAction(item, '普通')" class="control_btn minus_btn"></image>
                    <text v-if="item.dishNumber > 0" class="quantity_text">{{ item.dishNumber }}</text>
                    <image src="../../static/btn_add.png" class="control_btn plus_btn"
                      @click="addDishAction(item, '普通')"></image>
                  </view>

                  <!-- 有规格的菜品按钮 -->
                  <view class="spec_button_wrapper" v-else>
                    <view class="spec_button" @click="moreNormDataesHandle(item)">选择规格</view>
                  </view>
                </view>
              </view>
            </view>
            <view class="bottom_spacer"></view>
          </scroll-view>

          <!-- 无菜品提示 -->
          <view class="empty_dishes" v-else>
            <view v-if="typeListData.length > 0" class="empty_text">该分类下暂无菜品</view>
          </view>
        </view>
      </view>

      <!-- 店铺打烊提示 -->
      <view class="shop_closed" v-if="shopStatus === 0">
        <text class="closed_text">店铺已打烊</text>
      </view>

      <!-- 底部遮罩 -->
      <view class="bottom_mask"></view>

      <!-- 底部购物车 -->
      <view class="shopping_cart_bar">
        <!-- 空购物车状态 -->
        <view class="cart_empty" v-if="orderListData().length === 0 || shopStatus !== 1">
          <view class="cart_icon_wrapper">
            <image src="../../static/btn_waiter_nor.png" class="cart_icon"></image>
          </view>
          <view class="cart_info">
            <view class="total_price">
              <text class="currency">￥</text>
              <text class="amount">0</text>
            </view>
          </view>
          <view class="checkout_btn disabled">去结算</view>
        </view>

        <!-- 有商品的购物车状态 -->
        <view class="cart_active" v-else>
          <view class="cart_content" @click="() => (openOrderCartList = !openOrderCartList)">
            <view class="cart_icon_wrapper">
              <image src="../../static/btn_waiter_sel.png" class="cart_icon"></image>
              <view class="item_count">{{ orderDishNumber }}</view>
            </view>
            <view class="cart_info">
              <view class="total_price">
                <text class="currency">￥</text>
                <text class="amount">{{ orderDishPrice.toFixed(2) }}</text>
              </view>
            </view>
          </view>
          <view class="checkout_btn active" @click="goOrder()">去结算</view>
        </view>
      </view>
      <!-- end -->
      <!-- 选择多规格弹层 - start -->
      <view class="pop_mask" v-show="openMoreNormPop">
        <popMask :moreNormDishdata="moreNormDishdata" :moreNormdata="moreNormdata" :flavorDataes="flavorDataes"
          @checkMoreNormPop="checkMoreNormPop" @addShop="addShop" @closeMoreNorm="closeMoreNorm"></popMask>
      </view>
      <!-- 选择多规格 - end -->
      <!-- 菜品详情弹层 - start -->
      <!-- openDetailHandle 这个函数触发的菜品详情 -->
      <view class="pop_mask" v-show="openDetailPop" style="z-index: 9999">
        <dishDetail :dishDetailes="dishDetailes" :openDetailPop="openDetailPop" :dishMealData="dishMealData"
          @redDishAction="redDishAction" @addDishAction="addDishAction" @moreNormDataesHandle="moreNormDataesHandle"
          @dishClose="dishClose"></dishDetail>
      </view>
      <!-- 菜品详情 - end -->
      <!-- 购物车弹框 - start -->
      <view class="pop_mask" v-show="openOrderCartList" @click="openOrderCartList = !openOrderCartList">
        <popCart :openOrderCartLis="openOrderCartList" :orderAndUserInfo="orderAndUserInfo"
          @clearCardOrder="clearCardOrder" @addDishAction="addDishAction" @redDishAction="redDishAction"></popCart>
      </view>
      <!-- 购物车弹框 - end -->
      <view class="pop_mask" v-show="loaddingSt">
        <view class="lodding">
          <image class="lodding_ico" src="../../static/lodding.gif" mode=""></image>
          <view class="lodding_text">加载中...</view>
        </view>
      </view>
      <!-- 电话弹层 -->
      <phone ref="phone" :phoneData="phoneData" @closePopup="closePopup"></phone>
      <!-- end -->
      <!-- 店面打烊弹层 -->
      <view class="colseShop" v-if="shopStatus === 0">
        <view class="shop">本店已打样</view>
      </view>
      <!-- end -->
    </view>
  </view>
</template>
<script src="./index.js"></script>
<style src="./style.scss" lang="scss" scoped></style>
<style scoped>
/* #ifdef MP-WEIXIN || APP-PLUS */
::v-deep ::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}

/* #endif */
</style>
