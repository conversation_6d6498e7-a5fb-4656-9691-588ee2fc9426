<view class="page-container data-v-57280228"><nav-bar vue-id="8dd740cc-1" class="data-v-57280228" bind:__l="__l"></nav-bar><view class="home_content data-v-57280228" style="{{'padding-top:'+(ht+'px')+';'}}"><view class="restaurant_info_box data-v-57280228"><view class="restaurant_info data-v-57280228"><view class="info_header data-v-57280228"><view class="shop_logo data-v-57280228"><image class="logo_image data-v-57280228" src="../../static/logo_ruiji.png"></image></view><view class="shop_details data-v-57280228"><view class="shop_title data-v-57280228"><text class="shop_name data-v-57280228">{{currentShopInfo.shopName}}</text><view class="{{['business_status','data-v-57280228',(shopStatus===1)?'open':'',(shopStatus!==1)?'closed':'']}}">{{''+(shopStatus===1?'营业中':'休息中')+''}}</view></view><view class="shop_info_row data-v-57280228"><view class="info_item data-v-57280228"><image class="info_icon data-v-57280228" src="../../static/money.png"></image><text class="info_text data-v-57280228">{{"配送费"+$root.m0+"元"}}</text></view></view></view></view><view class="info_footer data-v-57280228"><view class="address_section data-v-57280228"><view class="shop_description data-v-57280228">{{currentShopInfo.shopName}}</view><view class="shop_address data-v-57280228"><image class="address_icon data-v-57280228" src="../../image/address.png"></image><text class="address_text data-v-57280228">{{currentShopInfo.shopAddress}}</text></view></view><view class="contact_section data-v-57280228"><view data-event-opts="{{[['tap',[['handlePhone',['bottom']]]]]}}" class="phone_button data-v-57280228" bindtap="__e"><image class="phone_icon data-v-57280228" src="../../image/phone.png"></image></view></view></view></view></view><block wx:if="{{shopStatus===1}}"><view class="restaurant_menu_list data-v-57280228"><view class="category_sidebar data-v-57280228"><scroll-view class="category_scroll data-v-57280228" scroll-y="{{true}}" scroll-with-animation="{{true}}" scroll-top="{{scrollTop+100}}" scroll-into-view="{{itemId}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['swichMenu',['$0',index],[[['typeListData','',index]]]]]]]}}" class="{{['category_item','data-v-57280228',(typeIndex==index)?'active':'']}}" catchtap="__e"><view class="{{['category_name','data-v-57280228',(item.g0>5)?'multi_line':'']}}">{{item.$orig.name}}</view></view></block><view class="bottom_spacer data-v-57280228"></view></scroll-view></view><view class="dishes_container data-v-57280228"><block wx:if="{{$root.g1}}"><scroll-view class="dishes_list data-v-57280228" scroll-y="true" scroll-top="0rpx"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="dish_card data-v-57280228"><view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_image_wrapper data-v-57280228" bindtap="__e"><image class="dish_image data-v-57280228" mode="aspectFill" src="{{item.$orig.image}}"></image></view><view class="dish_content data-v-57280228"><view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_header data-v-57280228" bindtap="__e"><text class="dish_title data-v-57280228">{{item.$orig.name}}</text></view><view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_description data-v-57280228" bindtap="__e">{{''+(item.$orig.description||item.$orig.name)+''}}</view><view data-event-opts="{{[['tap',[['openDetailHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="dish_sales data-v-57280228" bindtap="__e">月销量0</view><view class="dish_footer data-v-57280228"><view class="price_section data-v-57280228"><text class="currency_symbol data-v-57280228">￥</text><text class="price_value data-v-57280228">{{item.g2}}</text></view><block wx:if="{{item.g3}}"><view class="quantity_controls data-v-57280228"><block wx:if="{{item.$orig.dishNumber>=1}}"><image class="control_btn minus_btn data-v-57280228" src="../../static/btn_red.png" data-event-opts="{{[['tap',[['redDishAction',['$0','普通'],[[['dishListItems','',index]]]]]]]}}" bindtap="__e"></image></block><block wx:if="{{item.$orig.dishNumber>0}}"><text class="quantity_text data-v-57280228">{{item.$orig.dishNumber}}</text></block><image class="control_btn plus_btn data-v-57280228" src="../../static/btn_add.png" data-event-opts="{{[['tap',[['addDishAction',['$0','普通'],[[['dishListItems','',index]]]]]]]}}" bindtap="__e"></image></view></block><block wx:else><view class="spec_button_wrapper data-v-57280228"><view data-event-opts="{{[['tap',[['moreNormDataesHandle',['$0'],[[['dishListItems','',index]]]]]]]}}" class="spec_button data-v-57280228" bindtap="__e">选择规格</view></view></block></view></view></view></block><view class="bottom_spacer data-v-57280228"></view></scroll-view></block><block wx:else><view class="empty_dishes data-v-57280228"><block wx:if="{{$root.g4>0}}"><view class="empty_text data-v-57280228">该分类下暂无菜品</view></block></view></block></view></view></block><block wx:if="{{shopStatus===0}}"><view class="shop_closed data-v-57280228"><text class="closed_text data-v-57280228">店铺已打烊</text></view></block><view class="bottom_mask data-v-57280228"></view><view class="shopping_cart_bar data-v-57280228"><block wx:if="{{$root.g5}}"><view class="cart_empty data-v-57280228"><view class="cart_icon_wrapper data-v-57280228"><image class="cart_icon data-v-57280228" src="../../static/btn_waiter_nor.png"></image></view><view class="cart_info data-v-57280228"><view class="total_price data-v-57280228"><text class="currency data-v-57280228">￥</text><text class="amount data-v-57280228">0</text></view></view><view class="checkout_btn disabled data-v-57280228">去结算</view></view></block><block wx:else><view class="cart_active data-v-57280228"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="cart_content data-v-57280228" bindtap="__e"><view class="cart_icon_wrapper data-v-57280228"><image class="cart_icon data-v-57280228" src="../../static/btn_waiter_sel.png"></image><view class="item_count data-v-57280228">{{orderDishNumber}}</view></view><view class="cart_info data-v-57280228"><view class="total_price data-v-57280228"><text class="currency data-v-57280228">￥</text><text class="amount data-v-57280228">{{$root.g6}}</text></view></view></view><view data-event-opts="{{[['tap',[['goOrder']]]]}}" class="checkout_btn active data-v-57280228" bindtap="__e">去结算</view></view></block></view><view hidden="{{!(openMoreNormPop)}}" class="pop_mask data-v-57280228"><pop-mask vue-id="8dd740cc-2" moreNormDishdata="{{moreNormDishdata}}" moreNormdata="{{moreNormdata}}" flavorDataes="{{flavorDataes}}" data-event-opts="{{[['^checkMoreNormPop',[['checkMoreNormPop']]],['^addShop',[['addShop']]],['^closeMoreNorm',[['closeMoreNorm']]]]}}" bind:checkMoreNormPop="__e" bind:addShop="__e" bind:closeMoreNorm="__e" class="data-v-57280228" bind:__l="__l"></pop-mask></view><view hidden="{{!(openDetailPop)}}" class="pop_mask data-v-57280228" style="z-index:9999;"><dish-detail vue-id="8dd740cc-3" dishDetailes="{{dishDetailes}}" openDetailPop="{{openDetailPop}}" dishMealData="{{dishMealData}}" data-event-opts="{{[['^redDishAction',[['redDishAction']]],['^addDishAction',[['addDishAction']]],['^moreNormDataesHandle',[['moreNormDataesHandle']]],['^dishClose',[['dishClose']]]]}}" bind:redDishAction="__e" bind:addDishAction="__e" bind:moreNormDataesHandle="__e" bind:dishClose="__e" class="data-v-57280228" bind:__l="__l"></dish-detail></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" hidden="{{!(openOrderCartList)}}" class="pop_mask data-v-57280228" bindtap="__e"><pop-cart vue-id="8dd740cc-4" openOrderCartLis="{{openOrderCartList}}" orderAndUserInfo="{{orderAndUserInfo}}" data-event-opts="{{[['^clearCardOrder',[['clearCardOrder']]],['^addDishAction',[['addDishAction']]],['^redDishAction',[['redDishAction']]]]}}" bind:clearCardOrder="__e" bind:addDishAction="__e" bind:redDishAction="__e" class="data-v-57280228" bind:__l="__l"></pop-cart></view><view hidden="{{!(loaddingSt)}}" class="pop_mask data-v-57280228"><view class="lodding data-v-57280228"><image class="lodding_ico data-v-57280228" src="../../static/lodding.gif" mode></image><view class="lodding_text data-v-57280228">加载中...</view></view></view><phone vue-id="8dd740cc-5" phoneData="{{phoneData}}" data-ref="phone" data-event-opts="{{[['^closePopup',[['closePopup']]]]}}" bind:closePopup="__e" class="data-v-57280228 vue-ref" bind:__l="__l"></phone><block wx:if="{{shopStatus===0}}"><view class="colseShop data-v-57280228"><view class="shop data-v-57280228">本店已打样</view></view></block></view></view>