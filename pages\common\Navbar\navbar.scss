.navBar {
  position: fixed;
  display: flex;
  z-index: 999;
  top: 0;
  left: 0;
  width: 100vw;
  height: 304rpx;
  box-sizing: border-box;
  background: linear-gradient(184deg,
      rgba(0, 0, 0, 0.4) 25%,
      rgba(51, 51, 51, 0) 96%);
  backdrop-filter: blur(10rpx);
  /* #ifdef H5 */
  padding-top: 20rpx;
  /* #endif */

  .leftNav {
    .back {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .centerNav {
    width: calc(100vw - 176rpx);
    text-align: center;
    line-height: 88rpx;
    font-size: 36rpx;
    color: #fff;
    font-weight: 500;
  }

  .logo {
    height: 66rpx;
    width: 184rpx;
  }

  .index_bg {
    width: 750rpx;
    height: 304rpx;
    object-fit: cover;
  }

  .test_image {
    height: 56rpx;
    width: 56rpx;
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .person-box {
    position: fixed;
    left: 38rpx;
    z-index: 9999;
    top: 100rpx;
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 32rpx;
    backdrop-filter: blur(10rpx);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
      transform: translateY(-2rpx);
    }

    .test_image {
      margin-right: 12rpx;
    }
  }

  .person-title {
    font-size: 26rpx;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #ffffff;
    line-height: 36rpx;
    letter-spacing: 0.5rpx;
  }
}