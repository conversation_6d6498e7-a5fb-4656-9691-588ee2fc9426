@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.navBar.data-v-6fde291d {
  position: fixed;
  display: flex;
  z-index: 999;
  top: 0;
  left: 0;
  width: 100vw;
  height: 304rpx;
  box-sizing: border-box;
  background: linear-gradient(184deg, rgba(0, 0, 0, 0.4) 25%, rgba(51, 51, 51, 0) 96%);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.navBar .leftNav .back.data-v-6fde291d {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.navBar .leftNav .back.data-v-6fde291d:hover {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.navBar .centerNav.data-v-6fde291d {
  width: calc(100vw - 176rpx);
  text-align: center;
  line-height: 88rpx;
  font-size: 36rpx;
  color: #fff;
  font-weight: 500;
}
.navBar .logo.data-v-6fde291d {
  height: 66rpx;
  width: 184rpx;
}
.navBar .index_bg.data-v-6fde291d {
  width: 750rpx;
  height: 304rpx;
  object-fit: cover;
}
.navBar .test_image.data-v-6fde291d {
  height: 56rpx;
  width: 56rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.navBar .person-box.data-v-6fde291d {
  position: fixed;
  left: 38rpx;
  z-index: 9999;
  top: 100rpx;
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 32rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}
.navBar .person-box.data-v-6fde291d:hover {
  background: rgba(0, 0, 0, 0.5);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.navBar .person-box .test_image.data-v-6fde291d {
  margin-right: 12rpx;
}
.navBar .person-title.data-v-6fde291d {
  font-size: 26rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
  color: #ffffff;
  line-height: 36rpx;
  letter-spacing: 0.5rpx;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.success_content.data-v-6fde291d {
  padding-top: 260rpx;
}
.success_content .success_info.data-v-6fde291d {
  text-align: center;
}
.success_content .success_info .success_icon.data-v-6fde291d {
  width: 144rpx;
  height: 138rpx;
  text-align: center;
}
.success_content .success_info .success_title.data-v-6fde291d {
  font-size: 40rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
  color: #333333;
  margin-top: 16rpx;
  height: 56rpx;
  line-height: 56rpx;
}
.success_content .success_info .success_desc.data-v-6fde291d {
  font-size: 32rpx;
  margin-bottom: 40rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: center;
  color: #666666;
  height: 44rpx;
  line-height: 44rp;
  margin-top: 24rpx;
}
.success_content .success_info .word-box.data-v-6fde291d {
  margin-top: 38rpx;
  height: 44rpx;
  line-height: 44rpx;
}
.success_content .success_info .word_bottom.data-v-6fde291d {
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: center;
  font-size: 32rpx;
  color: #666666;
}
.success_content .success_info .word_bottom .word_date.data-v-6fde291d {
  color: #ffc200;
}
.success_content .success_info .btns.data-v-6fde291d {
  width: 750rpx;
  display: flex;
  justify-content: center;
}
.success_content .success_info .btns .go_dish.data-v-6fde291d {
  position: relative;
  font-size: 30rpx;
  width: 200rpx;
  height: 72rpx;
  line-height: 72rpx;
  margin-top: 20rpx;
  background: #ffc200;
  border-radius: 8rpx;
}
.success_content .success_info .btns .go_dish + .go_dish.data-v-6fde291d {
  margin-left: 40rpx;
}
.success_content .success_info .btns .defaultBtn.data-v-6fde291d {
  border: 1px solid #e5e4e4;
  background: none;
}

