@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-57280228 {
  background-color: #f8f9fa;
  min-height: 100vh;
}
.home_content.data-v-57280228 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 160rpx;
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: #f8f9fa;
}
.home_content .restaurant_info_box.data-v-57280228 {
  position: fixed;
  top: 160rpx;
  left: 0;
  right: 0;
  z-index: 10;
  width: 100%;
  background: linear-gradient(184deg, rgba(0, 0, 0, 0.35) 25%, rgba(51, 51, 51, 0) 96%);
}
.home_content .restaurant_info_box .restaurant_info.data-v-57280228 {
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.home_content .restaurant_info_box .restaurant_info .info_header.data-v-57280228 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid #ebebeb;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_logo.data-v-57280228 {
  margin-right: 24rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_logo .logo_image.data-v-57280228 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details.data-v-57280228 {
  flex: 1;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_title.data-v-57280228 {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_title .shop_name.data-v-57280228 {
  font-size: 36rpx;
  font-weight: 600;
  color: #20232a;
  margin-right: 16rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_title .business_status.data-v-57280228 {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_title .business_status.open.data-v-57280228 {
  background: rgba(29, 199, 121, 0.1);
  color: #1dc779;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_title .business_status.closed.data-v-57280228 {
  background: rgba(153, 153, 153, 0.1);
  color: #999999;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_info_row.data-v-57280228 {
  display: flex;
  align-items: center;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_info_row .info_item.data-v-57280228 {
  display: flex;
  align-items: center;
  margin-right: 32rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_info_row .info_item .info_icon.data-v-57280228 {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_info_row .info_item .info_text.data-v-57280228 {
  font-size: 26rpx;
  color: #666666;
}
.home_content .restaurant_info_box .restaurant_info .info_footer.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_footer .address_section.data-v-57280228 {
  flex: 1;
}
.home_content .restaurant_info_box .restaurant_info .info_footer .address_section .shop_description.data-v-57280228 {
  font-size: 28rpx;
  color: #20232a;
  margin-bottom: 8rpx;
  font-weight: 500;
}
.home_content .restaurant_info_box .restaurant_info .info_footer .address_section .shop_address.data-v-57280228 {
  display: flex;
  align-items: center;
}
.home_content .restaurant_info_box .restaurant_info .info_footer .address_section .shop_address .address_icon.data-v-57280228 {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_footer .address_section .shop_address .address_text.data-v-57280228 {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}
.home_content .restaurant_info_box .restaurant_info .info_footer .contact_section .phone_button.data-v-57280228 {
  width: 72rpx;
  height: 72rpx;
  background: rgba(233, 78, 60, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.home_content .restaurant_info_box .restaurant_info .info_footer .contact_section .phone_button .phone_icon.data-v-57280228 {
  width: 32rpx;
  height: 32rpx;
}
.home_content .restaurant_menu_list.data-v-57280228 {
  display: flex;
  width: 100%;
  height: calc(100vh - 400rpx);
  margin-top: 260rpx;
  flex: 1;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.home_content .restaurant_menu_list .category_sidebar.data-v-57280228 {
  width: 180rpx;
  background: #f8f9fa;
  border-right: 1px solid #ebebeb;
}
.home_content .restaurant_menu_list .category_sidebar .category_scroll.data-v-57280228 {
  height: 100%;
  padding: 16rpx 0;
}
.home_content .restaurant_menu_list .category_sidebar .category_item.data-v-57280228 {
  padding: 24rpx 16rpx;
  margin: 8rpx 12rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.home_content .restaurant_menu_list .category_sidebar .category_item .category_name.data-v-57280228 {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  line-height: 1.3;
  font-weight: 400;
}
.home_content .restaurant_menu_list .category_sidebar .category_item .category_name.multi_line.data-v-57280228 {
  line-height: 1.2;
}
.home_content .restaurant_menu_list .category_sidebar .category_item.active.data-v-57280228 {
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.home_content .restaurant_menu_list .category_sidebar .category_item.active .category_name.data-v-57280228 {
  color: #e94e3c;
  font-weight: 600;
}
.home_content .restaurant_menu_list .category_sidebar .bottom_spacer.data-v-57280228 {
  height: 160rpx;
}
.home_content .restaurant_menu_list .dishes_container.data-v-57280228 {
  flex: 1;
  background: #ffffff;
}
.home_content .restaurant_menu_list .dishes_container .dishes_list.data-v-57280228 {
  height: 100%;
  padding: 16rpx 0;
}
.home_content .restaurant_menu_list .dishes_container .dish_card.data-v-57280228 {
  display: flex;
  padding: 24rpx;
  border-bottom: 1px solid rgba(235, 235, 235, 0.5);
  transition: background-color 0.2s ease;
}
.home_content .restaurant_menu_list .dishes_container .dish_card.data-v-57280228:hover {
  background: rgba(248, 249, 250, 0.3);
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_image_wrapper.data-v-57280228 {
  margin-right: 24rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_image_wrapper .dish_image.data-v-57280228 {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content.data-v-57280228 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_header .dish_title.data-v-57280228 {
  font-size: 32rpx;
  font-weight: 600;
  color: #20232a;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_description.data-v-57280228 {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 6rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_sales.data-v-57280228 {
  font-size: 22rpx;
  color: #999999;
  margin-bottom: 16rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .price_section.data-v-57280228 {
  display: flex;
  align-items: baseline;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .price_section .currency_symbol.data-v-57280228 {
  font-size: 24rpx;
  color: #e94e3c;
  margin-right: 4rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .price_section .price_value.data-v-57280228 {
  font-size: 36rpx;
  font-weight: 600;
  color: #e94e3c;
  font-family: DIN, DIN-Medium;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .quantity_controls.data-v-57280228 {
  display: flex;
  align-items: center;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .quantity_controls .control_btn.data-v-57280228 {
  width: 60rpx;
  height: 60rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .quantity_controls .control_btn.minus_btn.data-v-57280228 {
  margin-right: 16rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .quantity_controls .control_btn.plus_btn.data-v-57280228 {
  margin-left: 16rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .quantity_controls .quantity_text.data-v-57280228 {
  min-width: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #20232a;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .spec_button_wrapper .spec_button.data-v-57280228 {
  padding: 12rpx 24rpx;
  background: #ffc200;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #20232a;
  text-align: center;
}
.home_content .restaurant_menu_list .dishes_container .bottom_spacer.data-v-57280228 {
  height: 160rpx;
}
.home_content .restaurant_menu_list .empty_dishes.data-v-57280228 {
  flex: 1;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.home_content .restaurant_menu_list .empty_dishes .empty_text.data-v-57280228 {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}
.home_content .shop_closed.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 260rpx;
  padding: 80rpx 0;
}
.home_content .shop_closed .closed_text.data-v-57280228 {
  font-size: 48rpx;
  color: #666666;
  font-weight: 500;
}
.home_content .shopping_cart_bar.data-v-57280228 {
  position: fixed;
  bottom: 32rpx;
  left: 30rpx;
  right: 30rpx;
  height: 96rpx;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 48rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  z-index: 99;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.home_content .shopping_cart_bar .cart_empty.data-v-57280228 {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 24rpx;
}
.home_content .shopping_cart_bar .cart_empty .cart_icon_wrapper.data-v-57280228 {
  position: relative;
  margin-right: 24rpx;
}
.home_content .shopping_cart_bar .cart_empty .cart_icon_wrapper .cart_icon.data-v-57280228 {
  width: 96rpx;
  height: 96rpx;
}
.home_content .shopping_cart_bar .cart_empty .cart_info.data-v-57280228 {
  flex: 1;
}
.home_content .shopping_cart_bar .cart_empty .cart_info .total_price.data-v-57280228 {
  display: flex;
  align-items: baseline;
}
.home_content .shopping_cart_bar .cart_empty .cart_info .total_price .currency.data-v-57280228 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 4rpx;
}
.home_content .shopping_cart_bar .cart_empty .cart_info .total_price .amount.data-v-57280228 {
  font-size: 36rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  font-family: DIN, DIN-Medium;
}
.home_content .shopping_cart_bar .cart_empty .checkout_btn.data-v-57280228 {
  padding: 0 32rpx;
  height: 72rpx;
  line-height: 72rpx;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
}
.home_content .shopping_cart_bar .cart_empty .checkout_btn.disabled.data-v-57280228 {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
}
.home_content .shopping_cart_bar .cart_active.data-v-57280228 {
  display: flex;
  align-items: center;
  height: 100%;
}
.home_content .shopping_cart_bar .cart_active .cart_content.data-v-57280228 {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}
.home_content .shopping_cart_bar .cart_active .cart_content .cart_icon_wrapper.data-v-57280228 {
  position: relative;
  margin-right: 24rpx;
}
.home_content .shopping_cart_bar .cart_active .cart_content .cart_icon_wrapper .cart_icon.data-v-57280228 {
  width: 96rpx;
  height: 96rpx;
}
.home_content .shopping_cart_bar .cart_active .cart_content .cart_icon_wrapper .item_count.data-v-57280228 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  padding: 0 8rpx;
  background: #e94e3c;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
}
.home_content .shopping_cart_bar .cart_active .cart_content .cart_info.data-v-57280228 {
  flex: 1;
}
.home_content .shopping_cart_bar .cart_active .cart_content .cart_info .total_price.data-v-57280228 {
  display: flex;
  align-items: baseline;
}
.home_content .shopping_cart_bar .cart_active .cart_content .cart_info .total_price .currency.data-v-57280228 {
  font-size: 24rpx;
  color: #ffffff;
  margin-right: 4rpx;
}
.home_content .shopping_cart_bar .cart_active .cart_content .cart_info .total_price .amount.data-v-57280228 {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  font-family: DIN, DIN-Medium;
}
.home_content .shopping_cart_bar .cart_active .checkout_btn.data-v-57280228 {
  margin-right: 12rpx;
  padding: 0 32rpx;
  height: 72rpx;
  line-height: 72rpx;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
}
.home_content .shopping_cart_bar .cart_active .checkout_btn.active.data-v-57280228 {
  background: #ffc200;
  color: #20232a;
}
.home_content .pop_mask.data-v-57280228 {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 9;
  background-color: rgba(0, 0, 0, 0.4);
}
.home_content .pop_mask .pop.data-v-57280228 {
  width: 60%;
  position: relative;
  top: 40%;
  left: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
          transform: translateX(-50%) translateY(-50%);
  background: #fff;
  border-radius: 20rpx;
}
.home_content .pop_mask .pop .open_table_cont.data-v-57280228 {
  padding-top: 60rpx;
}
.home_content .pop_mask .pop .open_table_cont .cont_tit.data-v-57280228 {
  font-size: 36rpx;
  color: #20232a;
  text-align: center;
}
.home_content .pop_mask .pop .open_table_cont .people_num_act.data-v-57280228 {
  display: flex;
  width: 60%;
  margin: 0 auto;
}
.home_content .pop_mask .pop .open_table_cont .people_num_act .red.data-v-57280228,
.home_content .pop_mask .pop .open_table_cont .people_num_act .add.data-v-57280228 {
  width: 112rpx;
  height: 112rpx;
}
.home_content .pop_mask .pop .open_table_cont .people_num_act .people_num.data-v-57280228 {
  line-height: 112rpx;
  flex: 1;
  text-align: center;
  font-size: 30rpx;
  color: #20232a;
}
.home_content .pop_mask .pop .butList.data-v-57280228 {
  background: #f7f7f7;
  display: flex;
  text-align: center;
  border-radius: 20rpx;
}
.home_content .pop_mask .pop .butList .define.data-v-57280228 {
  flex: 1;
  font-size: 36rpx;
  line-height: 100rpx;
}
.home_content .pop_mask .pop .butList .cancel.data-v-57280228 {
  flex: 1;
  font-size: 36rpx;
  line-height: 100rpx;
}
.home_content .pop_mask .more_norm_pop.data-v-57280228 {
  width: calc(100vw - 160rpx);
  box-sizing: border-box;
  position: relative;
  top: 50%;
  left: 50%;
  padding: 40rpx;
  -webkit-transform: translateX(-50%) translateY(-50%);
          transform: translateX(-50%) translateY(-50%);
  background: #fff;
  border-radius: 20rpx;
}
.home_content .pop_mask .more_norm_pop .div_big_image.data-v-57280228 {
  width: 100%;
  border-radius: 10rpx;
}
.home_content .pop_mask .more_norm_pop .title.data-v-57280228 {
  font-size: 40rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
}
.home_content .pop_mask .more_norm_pop .items_cont.data-v-57280228 {
  display: flex;
  flex-wrap: wrap;
  margin-left: -14rpx;
  max-height: 50vh;
}
.home_content .pop_mask .more_norm_pop .items_cont .item_row .flavor_name.data-v-57280228 {
  height: 40rpx;
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #666666;
  line-height: 40rpx;
  padding-left: 10rpx;
  padding-top: 20rpx;
}
.home_content .pop_mask .more_norm_pop .items_cont .item_row .flavor_item.data-v-57280228 {
  display: flex;
  flex-wrap: wrap;
}
.home_content .pop_mask .more_norm_pop .items_cont .item_row .flavor_item .item.data-v-57280228 {
  border: 1px solid #ffb302;
  border-radius: 12rpx;
  margin: 20rpx 10rpx;
  padding: 0 26rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  color: #333333;
}
.home_content .pop_mask .more_norm_pop .items_cont .item_row .flavor_item .act.data-v-57280228 {
  background: #ffc200;
  border: 1px solid #ffc200;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
}
.home_content .pop_mask .more_norm_pop .but_item.data-v-57280228 {
  display: flex;
  position: relative;
  flex: 1;
  padding-left: 10rpx;
  margin: 34rpx 0 -20rpx 0;
}
.home_content .pop_mask .more_norm_pop .but_item .price.data-v-57280228 {
  text-align: left;
  color: #e94e3c;
  line-height: 88rpx;
  box-sizing: border-box;
  font-size: 48rpx;
  font-family: DIN, DIN-Medium;
  font-weight: 500;
}
.home_content .pop_mask .more_norm_pop .but_item .price .ico.data-v-57280228 {
  font-size: 28rpx;
}
.home_content .pop_mask .more_norm_pop .but_item .active.data-v-57280228 {
  position: absolute;
  right: 0rpx;
  bottom: 20rpx;
  display: flex;
}
.home_content .pop_mask .more_norm_pop .but_item .active .dish_add.data-v-57280228,
.home_content .pop_mask .more_norm_pop .but_item .active .dish_red.data-v-57280228 {
  display: block;
  width: 72rpx;
  height: 72rpx;
}
.home_content .pop_mask .more_norm_pop .but_item .active .dish_number.data-v-57280228 {
  padding: 0 10rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
}
.home_content .pop_mask .more_norm_pop .but_item .active .dish_card_add.data-v-57280228 {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-weight: 500;
  font-size: 28rpx;
  opacity: 1;
  background: #ffc200;
  border-radius: 30rpx;
}
.home_content .pop_mask .lodding.data-v-57280228 {
  position: relative;
  top: 40%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.home_content .pop_mask .lodding .lodding_ico.data-v-57280228 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 100%;
}
.home_content .pop_mask .lodding .lodding_text.data-v-57280228 {
  margin-top: 20rpx;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}
.home_content .pop_mask .close.data-v-57280228 {
  position: absolute;
  bottom: -180rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.home_content .pop_mask .close .close_img.data-v-57280228 {
  width: 88rpx;
  height: 88rpx;
}
.home_content .bottom_mask.data-v-57280228 {
  position: absolute;
  height: 160rpx;
  width: 100%;
  bottom: 0;
  background: linear-gradient(to top, rgba(248, 249, 250, 0.8), transparent);
  pointer-events: none;
}
.class-item.data-v-57280228 {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.class-item.data-v-57280228:hover {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.class-item.data-v-57280228:last-child {
  min-height: 100vh;
}
@-webkit-keyframes fadeInUp-data-v-57280228 {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-57280228 {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@-webkit-keyframes pulse-data-v-57280228 {
0%,
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
}
@keyframes pulse-data-v-57280228 {
0%,
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
}
@media screen and (max-width: 750rpx) {
.home_content .restaurant_info_box .restaurant_info.data-v-57280228 {
    margin: 16rpx 20rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_header.data-v-57280228 {
    padding: 20rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_logo .logo_image.data-v-57280228 {
    width: 80rpx;
    height: 80rpx;
}
.home_content .restaurant_info_box .restaurant_info .info_header .shop_details .shop_title .shop_name.data-v-57280228 {
    font-size: 32rpx;
}
.home_content .restaurant_menu_list .category_sidebar.data-v-57280228 {
    width: 160rpx;
}
.home_content .restaurant_menu_list .category_sidebar .category_item.data-v-57280228 {
    padding: 20rpx 12rpx;
}
.home_content .restaurant_menu_list .category_sidebar .category_item .category_name.data-v-57280228 {
    font-size: 26rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card.data-v-57280228 {
    padding: 20rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_image_wrapper .dish_image.data-v-57280228 {
    width: 160rpx;
    height: 160rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_header .dish_title.data-v-57280228 {
    font-size: 30rpx;
}
.home_content .restaurant_menu_list .dishes_container .dish_card .dish_content .dish_footer .price_section .price_value.data-v-57280228 {
    font-size: 32rpx;
}
}


.data-v-57280228 ::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}


